<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="upload-dialog" :class="{ 'upload-dialog-lg': files.length > 0 }">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
      </DialogHeader>
      <DialogDescription v-if="description">
        {{ description }}
      </DialogDescription>

      <div class="upload-content">
        <!-- 文件属性选择 -->
        <FileAttributeSelector v-model="attributes" :attribute-selectors="attributeSelectors"
          ref="attributeSelectorRef" />

        <!-- 文件上传区域 -->
        <div class="upload-section">
          <FileUploadArea v-model="files" :accept="accept" :multiple="multiple" :max-size="maxSize"
            :max-files="maxFiles" :allow-directories="allowDirectories" @files-change="handleFilesChange"
            @upload-strategy="handleUploadStrategy" />
        </div>
      </div>

      <DialogFooter class="upload-footer">
        <div class="upload-info">
          <span v-if="files.length > 0" class="file-summary">
            {{ files.length }} 个文件，总计 {{ formatFileSize(totalSize) }}
          </span>
        </div>

        <div class="upload-actions">
          <Button variant="outline" @click="handleCancel">
            取消
          </Button>
          <Button @click="handleConfirm" :disabled="!canConfirm">
            {{ confirmText }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import FileUploadArea from './FileUploadArea.vue'
import FileAttributeSelector, { type AttributeSelector } from './FileAttributeSelector.vue'

import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import filesApi from '@/api/services/files'

import { formatFileSize, detectUploadStrategy, type UploadStrategy } from '@/lib/upload-utils'

// 上传文件数据接口
export interface UploadData {
  files: File[]
  attributes: Record<string, string | number | (string | number)[]>
}

// Props
const props = withDefaults(defineProps<{
  open: boolean
  title?: string
  description?: string
  confirmText?: string
  attributeSelectors?: AttributeSelector[]
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxFiles?: number
  allowDirectories?: boolean
  initialFiles?: File[]
  onUpload?: (data: UploadData) => Promise<void> | void
}>(), {
  title: '文件上传',
  confirmText: '开始上传',
  description: '',
  attributeSelectors: () => [],
  accept: '',
  multiple: true,
  maxSize: Infinity, // 移除文件大小限制
  maxFiles: Infinity, // 移除文件数量限制
  allowDirectories: true,
  initialFiles: () => []
})

// Emits
const emit = defineEmits<{
  'update:open': [open: boolean]
  'confirm': [data: UploadData]
  'cancel': []
  'upload-success': [data: UploadData]
  'upload-error': [error: string]
  'files-uploaded': []
}>()

// 响应式数据
const files = ref<File[]>([])
const attributes = ref<Record<string, string | number | (string | number)[]>>({})
const uploadStrategy = ref<UploadStrategy | null>(null)

// 属性选择器组件引用
const attributeSelectorRef = ref<InstanceType<typeof FileAttributeSelector> | null>(null)

// 计算属性
const totalSize = computed(() => {
  return files.value.reduce((total, file) => total + file.size, 0)
})

const canConfirm = computed(() => {
  // 检查是否有文件
  if (files.value.length === 0) return false

  // 使用属性选择器组件的验证方法
  return attributeSelectorRef.value?.validateRequiredFields() ?? true
})

// Composables
const route = useRoute()
const tusUpload = useTusUpload()

const categoryId = computed((): string => {
  const queryId = route.query.categoryId as string
  return queryId || ''
})

const parentId = computed((): string => {
  // 首先检查路由查询参数中是否有 parentId（点击文件夹时传递的）
  const routeParentId = route.query.parentId as string
  if (routeParentId) {
    return routeParentId
  }

  // 如果没有 parentId，使用 categoryId 作为默认值
  return categoryId.value || ''
})

// 监听弹窗打开状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    // 重置状态
    uploadStrategy.value = null
    // 属性重置由子组件自动处理
    attributes.value = {}

    // 如果有初始文件，设置到文件列表中；否则清空
    if (props.initialFiles && props.initialFiles.length > 0) {
      console.log(`📁 UploadDialog: 设置初始文件 ${props.initialFiles.length} 个`)
      files.value = [...props.initialFiles]

      // 重新计算上传策略
      const strategy = detectUploadStrategy(props.initialFiles)
      uploadStrategy.value = strategy
      console.log('初始文件上传策略:', strategy)
    } else {
      files.value = []
    }
  }
})

// 处理弹窗打开状态变化
const handleOpenChange = (open: boolean) => {
  emit('update:open', open)
}

// 处理文件变化
const handleFilesChange = (newFiles: File[]) => {
  files.value = newFiles

  // 如果有文件，但没有策略，需要重新检测策略
  if (newFiles.length > 0 && !uploadStrategy.value) {
    const strategy = detectUploadStrategy(newFiles)
    uploadStrategy.value = strategy
    console.log('文件变化时重新检测策略:', strategy)
  }
  // 如果没有文件了，清空策略
  else if (newFiles.length === 0) {
    uploadStrategy.value = null
  }
}

// 处理上传策略变化
const handleUploadStrategy = (strategy: UploadStrategy) => {
  uploadStrategy.value = strategy
  console.log('上传策略更新:', strategy)
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  emit('update:open', false)
}

// 处理确认上传
const handleConfirm = async () => {
  if (!canConfirm.value) return

  const uploadData: UploadData = {
    files: files.value,
    attributes: { ...attributes.value }
  }

  try {
    // 发送确认事件
    emit('confirm', uploadData)

    // 检查是否在 Electron 环境中
    if (!tusUpload.isElectron.value) {
      toast.error('上传功能仅在桌面应用中可用')
      if (props.onUpload) {
        await props.onUpload(uploadData)
      }
      emit('update:open', false)
      return
    }

    // 直接使用智能上传
    if (files.value.length > 0) {
      // 将属性直接转换为JSON字符串，存储在metadata的attributes字段中
      const metadata: Record<string, string> = {
        attributes: JSON.stringify(attributes.value)
      }

      // 定义上传回调
      const uploadCallbacks = {
        onFileUploaded: async (file: File, task: any) => {

          try {
            // 解析用户设置的属性
            let userAttributes: Record<string, any> = {}
            if (task.metadata?.attributes) {
              userAttributes = JSON.parse(task.metadata.attributes)
            }

            const startTaskData = {
              upload_url: task.uploadUrl,
              category_id: categoryId.value,
              parent_id: parentId.value,
              // 单文件使用文件名，文件夹使用相对路径
              relative_path: task.metadata?.relativePath || file.name,
              // 如果是空文件夹，添加is_folder参数
              is_folder: task.isFolder ? 1 : 0,
              ...userAttributes
            }

            // 调用后端接口同步文件信息
            const response = await filesApi.startTask(startTaskData)

            if (response.code !== 0) {
              console.error(`文件 ${file.name} 同步失败:`, response.msg)
            }
          } catch (error) {
            console.error(`文件 ${file.name} 同步过程中出现错误:`, error)
            const errorMessage = error instanceof Error ? error.message : '同步失败'
            toast.error(`文件 ${file.name} 同步失败: ${errorMessage}`)
          }
        },
        onAllFilesUploaded: (_files: File[], _tasks: any[]) => {
          // 等待3s后触发文件列表刷新事件
          setTimeout(() => {
            emit('files-uploaded')
          }, 3000)
        },
        onUploadError: (error: string, failedTasks: any[]) => {
          console.error('上传过程中出现错误:', error, failedTasks)
          toast.error(`上传失败: ${error}`)
        }
      }

      // 异步启动上传，不等待结果
      tusUpload.uploadFiles(files.value, metadata, uploadCallbacks).catch((error: Error) => {
        console.error('上传任务启动失败:', error)
        const errorMessage = error instanceof Error ? error.message : '上传启动失败'
        toast.error(errorMessage)
      })
    } else {
      toast.error('没有选择文件')
      return
    }

    // 如果有自定义上传处理函数，也执行它
    if (props.onUpload) {
      await props.onUpload(uploadData)
    }

    // 立即关闭弹窗，不等待上传完成
    emit('upload-success', uploadData)
    emit('update:open', false)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传启动失败'
    console.error('上传启动错误:', error)
    toast.error(errorMessage)
    emit('upload-error', errorMessage)
  }
}
</script>

<style scoped>
.upload-dialog {
  max-width: 600px;
  display: flex;
  flex-direction: column;
}

.upload-dialog-lg {
  max-width: 700px;
}

.upload-content {
  flex: 1;
  min-height: 0;
  max-height: 70vh;
  overflow-y: auto;
  padding: 0.5rem 1rem 0.5rem 0.1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.upload-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.upload-footer {
  border-top: 1px solid hsl(var(--border));
  margin-top: 1rem;
  padding-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.upload-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.file-summary {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.error-count {
  font-size: 0.875rem;
  color: hsl(var(--destructive));
}

.upload-actions {
  display: flex;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .upload-dialog {
    max-width: 95vw;
    max-height: 90vh;
  }

  .upload-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .upload-actions {
    width: 100%;
    justify-content: stretch;
  }

  .upload-actions .btn {
    flex: 1;
  }
}
</style>