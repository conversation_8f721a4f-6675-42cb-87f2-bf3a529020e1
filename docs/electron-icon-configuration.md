# Electron 应用程序图标配置

## 概述

本文档说明了如何为 Electron 应用程序配置图标，包括开发环境和打包后的应用程序图标设置。

## 图标文件信息

### 当前图标文件
- **文件路径**: `/Users/<USER>/Project/cloudDrive/logo.png`
- **文件格式**: PNG
- **图像尺寸**: 943 x 943 像素
- **颜色深度**: 8-bit/color RGBA
- **是否交错**: 非交错

### 图标要求
- **推荐尺寸**: 512x512 或更高（当前 943x943 符合要求）
- **支持格式**: PNG, ICO (Windows), ICNS (macOS)
- **透明背景**: 支持（当前文件为 RGBA 格式，支持透明度）

## 配置实现

### 1. 开发环境图标配置

#### `electron/main.ts` 修改
在 `createWindow()` 函数的 `BrowserWindow` 配置中添加了 `icon` 属性：

```typescript
mainWindow = new BrowserWindow({
  title: "星图",
  width: 1200,
  height: 800,
  minWidth: 800,
  minHeight: 600,
  show: false,
  autoHideMenuBar: true,
  icon: path.join(__dirname, "..", "logo.png"), // 设置应用程序图标
  webPreferences: {
    // ... 其他配置
  },
});
```

**路径说明**:
- `__dirname`: 指向编译后的 `dist-electron` 目录
- `".."`: 向上一级目录，指向项目根目录
- `"logo.png"`: 图标文件名

### 2. 打包配置

#### `electron-builder.yml` 修改
添加了全局和平台特定的图标配置：

```yaml
appId: com.clouddrive.app
productName: 星图

directories:
  output: release

# 应用程序图标配置
icon: logo.png

# Windows配置
win:
  icon: logo.png
  target:
    target: nsis
    arch:
      - x64

# macOS配置
mac:
  icon: logo.png
  target: dmg
```

## 跨平台兼容性

### Windows 平台
- **支持格式**: PNG, ICO
- **推荐尺寸**: 256x256 或更高
- **当前配置**: 使用 PNG 格式，electron-builder 会自动转换为 ICO

### macOS 平台
- **支持格式**: PNG, ICNS
- **推荐尺寸**: 512x512 或更高
- **当前配置**: 使用 PNG 格式，electron-builder 会自动转换为 ICNS

### Linux 平台
- **支持格式**: PNG
- **推荐尺寸**: 512x512 或更高
- **当前配置**: 直接使用 PNG 格式

## 图标显示位置

### 开发环境
- **窗口标题栏**: 显示应用程序图标
- **任务栏/Dock**: 显示应用程序图标
- **Alt+Tab 切换**: 显示应用程序图标

### 打包后应用
- **应用程序文件**: 可执行文件显示自定义图标
- **安装程序**: 安装向导显示应用程序图标
- **桌面快捷方式**: 快捷方式显示应用程序图标
- **开始菜单/应用程序文件夹**: 显示应用程序图标

## 验证方法

### 开发环境验证
1. 启动开发服务器：`pnpm dev:electron`
2. 检查窗口标题栏是否显示图标
3. 检查任务栏/Dock 是否显示图标
4. 使用 Alt+Tab (Windows/Linux) 或 Cmd+Tab (macOS) 检查图标

### 打包后验证
1. 构建应用程序：
   ```bash
   # macOS
   pnpm build:mac
   
   # Windows
   pnpm build:win
   ```

2. 检查生成的可执行文件图标
3. 安装应用程序并检查各处图标显示

## 故障排除

### 图标不显示的可能原因

1. **路径错误**
   - 检查 `electron/main.ts` 中的图标路径是否正确
   - 确保相对路径指向正确的文件位置

2. **文件格式问题**
   - 确保图标文件为有效的 PNG 格式
   - 检查文件是否损坏

3. **权限问题**
   - 确保应用程序有读取图标文件的权限

4. **缓存问题**
   - 清理构建缓存：`pnpm clean`
   - 重新构建应用程序

### 调试方法

1. **检查文件路径**
   ```typescript
   console.log('Icon path:', path.join(__dirname, "..", "logo.png"));
   ```

2. **检查文件存在性**
   ```typescript
   const iconPath = path.join(__dirname, "..", "logo.png");
   console.log('Icon exists:', fs.existsSync(iconPath));
   ```

## 最佳实践

### 图标设计建议
1. **尺寸**: 使用 512x512 或更高分辨率
2. **格式**: 优先使用 PNG 格式，支持透明背景
3. **设计**: 简洁明了，在小尺寸下仍然清晰可辨
4. **颜色**: 避免过于复杂的颜色渐变

### 多尺寸图标
如果需要更精细的控制，可以提供多个尺寸的图标：
```yaml
# electron-builder.yml
mac:
  icon: icons/icon.icns  # 包含多个尺寸的 ICNS 文件
win:
  icon: icons/icon.ico   # 包含多个尺寸的 ICO 文件
```

### 图标文件组织
```
project-root/
├── logo.png              # 主图标文件
├── icons/                 # 可选：多格式图标目录
│   ├── icon.ico          # Windows 图标
│   ├── icon.icns         # macOS 图标
│   └── icon.png          # Linux 图标
└── ...
```

## 注意事项

1. **文件位置**: 图标文件位于项目根目录，便于前端和 Electron 共同使用
2. **自动转换**: electron-builder 会自动将 PNG 转换为平台特定格式
3. **构建时间**: 大尺寸图标可能会增加构建时间
4. **文件大小**: 注意图标文件大小，避免过大影响应用程序体积

## 更新记录

- **2024-01-XX**: 初始配置，使用根目录 logo.png 作为应用程序图标
- 配置了开发环境和打包环境的图标显示
- 添加了跨平台兼容性支持
